const cron = require('node-cron');
const commonService = require('./commonService');
const emailService = require('./emailService');
const webhookService = require('./webhookService');
const db = require('../config/db');

class AutomationService {
    constructor() {
        this.scheduledTasks = new Map();
        this.automationRules = [];
        this.init();
    }

    init() {
        // Ensure database connection before loading automation rules
        this.ensureDatabaseConnection().then(() => {
            // Load automation rules from database
            this.loadAutomationRules();

            // Start scheduled tasks
            this.startScheduledTasks();
        }).catch(error => {
            console.error('Failed to initialize automation service:', error.message);
        });
    }

    /**
     * Ensure database connection is established
     */
    async ensureDatabaseConnection() {
        try {
            // Check if database connection exists
            if (!db.get()) {
                console.log('Database connection not found, attempting to connect...');
                db.connect('production');
            }
        } catch (error) {
            console.error('Database connection error:', error.message);
            setTimeout(() => {
                this.ensureDatabaseConnection();
            }, 1000);
        }
    }
    
    /**
     * Load automation rules from database
     */
    async loadAutomationRules() {
        try {
            // Ensure database connection before querying
            await this.ensureDatabaseConnection();

            const result = await commonService.getAllDataTable('automation_rules', { active: 1 });
            if (result.success && result.data) {
                this.automationRules = result.data;
                console.log(`Loaded ${this.automationRules.length} automation rules`);
            } else {
                console.log('No automation rules found or failed to load');
                this.automationRules = [];
            }
        } catch (error) {
            console.error('Failed to load automation rules:', error.message);
            this.automationRules = [];
        }
    }
    
    /**
     * Start scheduled tasks
     */
    startScheduledTasks() {
        // Send reminder emails every hour
        cron.schedule('0 * * * *', () => {
            this.processReminderEmails();
        });
        
        // Process automation rules every 5 minutes
        cron.schedule('*/5 * * * *', () => {
            this.processAutomationRules();
        });
        
        // Cleanup old logs daily at 2 AM
        cron.schedule('0 2 * * *', () => {
            this.cleanupOldLogs();
        });
        
        // Generate daily reports at 6 AM
        cron.schedule('0 6 * * *', () => {
            this.generateDailyReports();
        });
        
        console.log('Automation service started with scheduled tasks');
    }
    
    /**
     * Process reminder emails
     */
    async processReminderEmails() {
        try {
            console.log('Processing reminder emails...');

            // Ensure database connection
            await this.ensureDatabaseConnection();

            // Get surveys with reminder settings
            const surveysResult = await commonService.getListTable(`
                SELECT sc.*, p.name as project_name
                FROM survey_configs sc
                JOIN projects p ON sc.project_id = p.id
                WHERE sc.reminder_enabled = 1
                AND sc.active = 1
                AND (sc.expiry_date IS NULL OR sc.expiry_date > NOW())
            `, []);

            if (!surveysResult.success || !surveysResult.data || surveysResult.data.length === 0) {
                console.log('No surveys with reminder settings found');
                return;
            }

            for (const survey of surveysResult.data) {
                await this.processReminderForSurvey(survey);
            }

        } catch (error) {
            console.error('Error processing reminder emails:', error.message);
        }
    }
    
    /**
     * Process reminder for specific survey
     */
    async processReminderForSurvey(survey) {
        try {
            // Get survey invitations that need reminders
            const invitationsResult = await commonService.getListTable(`
                SELECT si.*
                FROM survey_invitations si
                LEFT JOIN survey_responses sr ON si.email = sr.respondent_email
                    AND sr.survey_config_id = si.survey_config_id
                WHERE si.survey_config_id = ?
                AND sr.id IS NULL  -- No response yet
                AND si.reminder_count < ?
                AND DATE_ADD(si.last_reminder_sent, INTERVAL ? HOUR) <= NOW()
            `, [
                survey.id,
                survey.max_reminders || 3,
                survey.reminder_interval_hours || 24
            ]);

            if (!invitationsResult.success || !invitationsResult.data || invitationsResult.data.length === 0) {
                console.log(`No invitations need reminders for survey ${survey.id}`);
                return;
            }

            for (const invitation of invitationsResult.data) {
                await this.sendReminderEmail(survey, invitation);
            }

        } catch (error) {
            console.error(`Error processing reminders for survey ${survey.id}:`, error.message);
        }
    }
    
    /**
     * Send reminder email
     */
    async sendReminderEmail(survey, invitation) {
        try {
            const reminderNumber = (invitation.reminder_count || 0) + 1;
            
            const emailResult = await emailService.sendSurveyReminder({
                to: invitation.email,
                surveyName: survey.name,
                surveyUrl: `${process.env.BASE_URL || 'http://localhost:3000'}/survey/${survey.survey_url_slug}`,
                reminderNumber: reminderNumber,
                expiryDate: survey.expiry_date
            });
            
            if (emailResult.success) {
                // Update invitation record
                await commonService.updateRecordTable({
                    reminder_count: reminderNumber,
                    last_reminder_sent: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }, 'survey_invitations', { id: invitation.id });
                
            } else {
                console.error(`Failed to send reminder to ${invitation.email}:`, emailResult.error);
            }
            
        } catch (error) {
            console.error('Error sending reminder email:', error.message);
        }
    }
    
    /**
     * Process automation rules
     */
    async processAutomationRules() {
        try {
            for (const rule of this.automationRules) {
                await this.executeAutomationRule(rule);
            }
        } catch (error) {
            console.error('Error processing automation rules:', error.message);
        }
    }
    
    /**
     * Execute automation rule
     */
    async executeAutomationRule(rule) {
        try {
            const ruleConfig = JSON.parse(rule.rule_config);
            
            // Check if rule conditions are met
            const conditionsMet = await this.checkRuleConditions(ruleConfig.conditions);
            
            if (conditionsMet) {
                // Execute rule actions
                for (const action of ruleConfig.actions) {
                    await this.executeRuleAction(action, rule);
                }
                
                // Update last executed time
                await commonService.updateRecordTable({
                    last_executed: new Date().toISOString(),
                    execution_count: (rule.execution_count || 0) + 1
                }, 'automation_rules', { id: rule.id });
            }
            
        } catch (error) {
            console.error(`Error executing automation rule ${rule.id}:`, error.message);
        }
    }
    
    /**
     * Check rule conditions
     */
    async checkRuleConditions(conditions) {
        try {
            for (const condition of conditions) {
                const result = await this.evaluateCondition(condition);
                if (!result) {
                    return false;
                }
            }
            return true;
        } catch (error) {
            console.error('Error checking rule conditions:', error.message);
            return false;
        }
    }
    
    /**
     * Evaluate single condition
     */
    async evaluateCondition(condition) {
        const { type, operator, value, target } = condition;
        
        switch (type) {
            case 'response_count':
                const countResult = await commonService.executeQuery(
                    'SELECT COUNT(*) as count FROM survey_responses WHERE survey_config_id = ?',
                    [target]
                );
                const count = countResult.data?.[0]?.count || 0;
                return this.compareValues(count, operator, value);
                
            case 'time_since_created':
                const surveyResult = await commonService.getAllDataTable('survey_configs', { id: target });
                if (!surveyResult.success || !surveyResult.data?.[0]) return false;
                
                const createdAt = new Date(surveyResult.data[0].created_at);
                const hoursSince = (Date.now() - createdAt.getTime()) / (1000 * 60 * 60);
                return this.compareValues(hoursSince, operator, value);
                
            case 'no_responses_for':
                const lastResponseResult = await commonService.executeQuery(
                    'SELECT MAX(submitted_at) as last_response FROM survey_responses WHERE survey_config_id = ?',
                    [target]
                );
                const lastResponse = lastResponseResult.data?.[0]?.last_response;
                if (!lastResponse) return true; // No responses yet
                
                const hoursSinceLastResponse = (Date.now() - new Date(lastResponse).getTime()) / (1000 * 60 * 60);
                return this.compareValues(hoursSinceLastResponse, operator, value);
                
            default:
                return false;
        }
    }
    
    /**
     * Compare values based on operator
     */
    compareValues(actual, operator, expected) {
        switch (operator) {
            case 'equals': return actual === expected;
            case 'greater_than': return actual > expected;
            case 'less_than': return actual < expected;
            case 'greater_equal': return actual >= expected;
            case 'less_equal': return actual <= expected;
            default: return false;
        }
    }
    
    /**
     * Execute rule action
     */
    async executeRuleAction(action, rule) {
        const { type, config } = action;
        
        switch (type) {
            case 'send_email':
                await this.executeEmailAction(config);
                break;
                
            case 'send_webhook':
                await this.executeWebhookAction(config);
                break;
                
            case 'generate_report':
                await this.executeReportAction(config);
                break;
                
            case 'close_survey':
                await this.executeCloseSurveyAction(config);
                break;
                
            default:
                console.warn(`Unknown action type: ${type}`);
        }
    }
    
    /**
     * Execute email action
     */
    async executeEmailAction(config) {
        try {
            await emailService.sendAdminNotification({
                to: config.recipients,
                subject: config.subject,
                message: config.message,
                priority: config.priority || 'normal',
                data: config.data
            });
        } catch (error) {
            console.error('Error executing email action:', error.message);
        }
    }
    
    /**
     * Execute webhook action
     */
    async executeWebhookAction(config) {
        try {
            await webhookService.sendCustomWebhook(
                {
                    url: config.webhook_url,
                    secret: config.webhook_secret,
                    headers: config.headers
                },
                config.event_type,
                config.data
            );
        } catch (error) {
            console.error('Error executing webhook action:', error.message);
        }
    }
    
    /**
     * Execute report action
     */
    async executeReportAction(config) {
        try {
            // Generate and send report
            // This would integrate with your reporting system
            console.log('Generating automated report:', config);
        } catch (error) {
            console.error('Error executing report action:', error.message);
        }
    }
    
    /**
     * Execute close survey action
     */
    async executeCloseSurveyAction(config) {
        try {
            await commonService.updateRecordTable({
                active: 0,
                closed_at: new Date().toISOString(),
                close_reason: 'Automated closure'
            }, 'survey_configs', { id: config.survey_id });
            
            console.log(`Survey ${config.survey_id} automatically closed`);
        } catch (error) {
            console.error('Error executing close survey action:', error.message);
        }
    }
    
    /**
     * Cleanup old logs
     */
    async cleanupOldLogs() {
        try {
            console.log('Cleaning up old logs...');
            
            // Cleanup webhook logs
            await webhookService.cleanupLogs(30);
            
            // Cleanup automation logs
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - 90);
            
            await commonService.executeQuery(
                'DELETE FROM automation_logs WHERE created_at < ?',
                [cutoffDate.toISOString()]
            );
            
            console.log('Log cleanup completed');
        } catch (error) {
            console.error('Error during log cleanup:', error.message);
        }
    }
    
    /**
     * Generate daily reports
     */
    async generateDailyReports() {
        try {
            console.log('Generating daily reports...');
            
            // Get surveys that need daily reports
            const surveysResult = await commonService.executeQuery(`
                SELECT * FROM survey_configs 
                WHERE daily_reports = 1 AND active = 1
            `);
            
            if (surveysResult.success && surveysResult.data) {
                for (const survey of surveysResult.data) {
                    await this.generateSurveyDailyReport(survey);
                }
            }
            
        } catch (error) {
            console.error('Error generating daily reports:', error.message);
        }
    }
    
    /**
     * Generate daily report for survey
     */
    async generateSurveyDailyReport(survey) {
        try {
            // Get yesterday's statistics
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            const yesterdayStr = yesterday.toISOString().split('T')[0];
            
            const statsResult = await commonService.executeQuery(`
                SELECT 
                    COUNT(*) as responses_count,
                    COUNT(DISTINCT respondent_email) as unique_respondents
                FROM survey_responses 
                WHERE survey_config_id = ? 
                AND DATE(submitted_at) = ?
            `, [survey.id, yesterdayStr]);
            
            if (statsResult.success && statsResult.data?.[0]) {
                const stats = statsResult.data[0];
                
                // Send report email
                await emailService.sendSurveyReport({
                    to: survey.report_recipients || survey.created_by,
                    surveyName: survey.name,
                    reportData: {
                        date: yesterday.toLocaleDateString(),
                        responses_count: stats.responses_count,
                        unique_respondents: stats.unique_respondents
                    }
                });
                
                console.log(`Daily report sent for survey: ${survey.name}`);
            }
            
        } catch (error) {
            console.error(`Error generating daily report for survey ${survey.id}:`, error.message);
        }
    }
    
    /**
     * Create automation rule
     */
    async createAutomationRule(ruleData) {
        try {
            const rule = {
                id: commonService.generateUUID(),
                name: ruleData.name,
                description: ruleData.description,
                rule_config: JSON.stringify(ruleData.config),
                active: ruleData.active !== false,
                created_by: ruleData.created_by,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };
            
            const result = await commonService.insertRecordTable(rule, 'automation_rules');
            
            if (result.success) {
                // Reload automation rules
                await this.loadAutomationRules();
            }
            
            return result;
        } catch (error) {
            console.error('Error creating automation rule:', error.message);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Update automation rule
     */
    async updateAutomationRule(ruleId, updateData) {
        try {
            const updates = {
                ...updateData,
                updated_at: new Date().toISOString()
            };
            
            if (updateData.config) {
                updates.rule_config = JSON.stringify(updateData.config);
                delete updates.config;
            }
            
            const result = await commonService.updateRecordTable(updates, 'automation_rules', { id: ruleId });
            
            if (result.success) {
                // Reload automation rules
                await this.loadAutomationRules();
            }
            
            return result;
        } catch (error) {
            console.error('Error updating automation rule:', error.message);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * Delete automation rule
     */
    async deleteAutomationRule(ruleId) {
        try {
            const result = await commonService.deleteRecordTable('automation_rules', { id: ruleId });
            
            if (result.success) {
                // Reload automation rules
                await this.loadAutomationRules();
            }
            
            return result;
        } catch (error) {
            console.error('Error deleting automation rule:', error.message);
            return { success: false, error: error.message };
        }
    }
}

module.exports = new AutomationService();
